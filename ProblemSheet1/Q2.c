/* 
========================================================================================
Name : Q2.c
Author: Subham Sourav
Description : Write a simple program to execute in an infinite loop at the background. Go to /proc directory and
identify all the process related information in the corresponding proc directory.
Date : 20-08-2024
========================================================================================
*/
#include <stdio.h>

int main()
{
  while(1)
  {}
  return 0;	  
}	  

/*
    Sample Execution:

 $ cc Q2.c
subham@subham-GF75:~/ProblemSheet1$ ./a.out
^C
*/
